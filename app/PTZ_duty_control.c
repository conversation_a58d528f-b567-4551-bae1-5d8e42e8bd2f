#include "PTZ_duty_control.h"
#include "stdlib.h"

extern TIM_HandleTypeDef htim1;

	uint32_t ccr_val = 500 + ((uint32_t)angle

void angle_to_control(uint8_t channel,uint8_t angle)
{
	if (angle > 180) angle = 180; * 2000) / 180;

	if (channel == 0)
		__HAL_TIM_SetCompare(&htim1,TIM_CHANNEL_1,ccr_val);
	else
		__HAL_TIM_SetCompare(&htim1,TIM_CHANNEL_2,ccr_val);
}

// 新增的浮点角度控制函数（支持0.5度精度）
void angle_to_control_float(uint8_t channel, float angle)
{
	// 限制角度范围在0-180度之间
	if (angle > 180.0f) angle = 180.0f;
	if (angle < 0.0f) angle = 0.0f;

	// 计算PWM占空比值，支持浮点精度
	uint32_t ccr_val = 500 + (uint32_t)(angle * 2000.0f / 180.0f);

	if (channel == 0)
		__HAL_TIM_SetCompare(&htim1,TIM_CHANNEL_1,ccr_val);
	else
		__HAL_TIM_SetCompare(&htim1,TIM_CHANNEL_2,ccr_val);
}
