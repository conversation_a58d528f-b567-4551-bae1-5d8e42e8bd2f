#!/usr/bin/env python3
"""
MaixPy 舵机控制视觉跟踪系统
基于PWM控制的双轴舵机视觉追踪
"""

import time
import math
from maix import camera, display, image, app, pwm, pinmap

# ==================== 配置参数 ====================
class Config:
    # 摄像头参数
    CAMERA_WIDTH = 320
    CAMERA_HEIGHT = 240
    
    # 舵机控制参数
    SERVO_X_PWM_ID = 6      # X轴舵机PWM ID
    SERVO_Y_PWM_ID = 7      # Y轴舵机PWM ID
    SERVO_X_PIN = "A18"     # X轴舵机引脚 (对应PWM6)
    SERVO_Y_PIN = "A19"     # Y轴舵机引脚 (对应PWM7)
    SERVO_FREQ = 50         # 舵机PWM频率 50Hz
    
    # 舵机占空比参数 (百分比)
    SERVO_MIN_DUTY = 2.5    # 最小占空比 2.5% -> 0.5ms
    SERVO_MAX_DUTY = 12.5   # 最大占空比 12.5% -> 2.5ms
    SERVO_CENTER_DUTY = 7.5 # 中心占空比 7.5% -> 1.5ms
    
    # 舵机角度范围
    SERVO_MIN_ANGLE = 0     # 最小角度
    SERVO_MAX_ANGLE = 180   # 最大角度
    SERVO_CENTER_ANGLE = 90 # 中心角度
    
    # 矩形检测参数
    RECT_THRESHOLD = 5000
    SHRINK_VALUE = 4.0
    MAX_LOOPS = 3
    MAX_DIFFERENCE = 8
    
    # 动态跟踪参数
    DYNAMIC_TRACKING = True
    RECT_LOST_THRESHOLD = 5
    RECT_SIMILARITY_THRESHOLD = 0.6
    MAX_RECT_DISTANCE = 30
    
    # 控制参数
    CONTROL_SENSITIVITY = 0.5  # 控制灵敏度
    CONTROL_DEADZONE = 10      # 控制死区
    MAX_STEP = 5               # 最大单步移动角度
    
    # 数据发送参数
    SEND_INTERVAL = 50  # 50ms发送间隔

# ==================== 数学工具函数 ====================
def calculate_distance(point1, point2):
    """计算两点间的欧几里得距离"""
    if not point1 or not point2:
        return float('inf')
    dx = point1[0] - point2[0]
    dy = point1[1] - point2[1]
    return math.sqrt(dx * dx + dy * dy)

def clamp(value, min_val, max_val):
    """限制数值范围"""
    return max(min_val, min(max_val, value))

def map_range(value, in_min, in_max, out_min, out_max):
    """映射数值范围"""
    return (value - in_min) * (out_max - out_min) / (in_max - in_min) + out_min

# ==================== 舵机控制类 ====================
class ServoController:
    def __init__(self):
        self.servo_x = None
        self.servo_y = None
        self.current_x_angle = Config.SERVO_CENTER_ANGLE
        self.current_y_angle = Config.SERVO_CENTER_ANGLE
        self.init_servos()
    
    def init_servos(self):
        """初始化舵机PWM"""
        try:
            # 配置引脚功能
            pinmap.set_pin_function(Config.SERVO_X_PIN, f"PWM{Config.SERVO_X_PWM_ID}")
            pinmap.set_pin_function(Config.SERVO_Y_PIN, f"PWM{Config.SERVO_Y_PWM_ID}")
            
            # 初始化X轴舵机 (水平)
            self.servo_x = pwm.PWM(Config.SERVO_X_PWM_ID, freq=Config.SERVO_FREQ, 
                                  duty=Config.SERVO_CENTER_DUTY, enable=True)
            # 初始化Y轴舵机 (垂直)
            self.servo_y = pwm.PWM(Config.SERVO_Y_PWM_ID, freq=Config.SERVO_FREQ, 
                                  duty=Config.SERVO_CENTER_DUTY, enable=True)
            
            # 设置初始位置到中心
            self.set_angle(Config.SERVO_CENTER_ANGLE, Config.SERVO_CENTER_ANGLE)
            
            print(f"舵机初始化成功:")
            print(f"  X轴舵机: {Config.SERVO_X_PIN} -> PWM{Config.SERVO_X_PWM_ID}")
            print(f"  Y轴舵机: {Config.SERVO_Y_PIN} -> PWM{Config.SERVO_Y_PWM_ID}")
            print(f"  PWM频率: {Config.SERVO_FREQ}Hz")
            
            return True
        except Exception as e:
            print(f"舵机初始化失败: {e}")
            return False
    
    def angle_to_duty(self, angle):
        """将角度转换为占空比"""
        # 角度范围: 0-180度
        # 占空比范围: 2.5%-12.5%
        angle = clamp(angle, Config.SERVO_MIN_ANGLE, Config.SERVO_MAX_ANGLE)
        duty = map_range(angle, 
                        Config.SERVO_MIN_ANGLE, Config.SERVO_MAX_ANGLE,
                        Config.SERVO_MIN_DUTY, Config.SERVO_MAX_DUTY)
        return duty
    
    def set_angle(self, x_angle, y_angle):
        """设置舵机角度"""
        try:
            if self.servo_x and self.servo_y:
                # 限制角度范围
                x_angle = clamp(x_angle, Config.SERVO_MIN_ANGLE, Config.SERVO_MAX_ANGLE)
                y_angle = clamp(y_angle, Config.SERVO_MIN_ANGLE, Config.SERVO_MAX_ANGLE)
                
                # 转换为占空比
                x_duty = self.angle_to_duty(x_angle)
                y_duty = self.angle_to_duty(y_angle)
                
                # 设置PWM占空比
                self.servo_x.duty(x_duty)
                self.servo_y.duty(y_duty)
                
                # 更新当前角度
                self.current_x_angle = x_angle
                self.current_y_angle = y_angle
                
                return True
        except Exception as e:
            print(f"舵机控制错误: {e}")
            return False
        return False
    
    def move_relative(self, delta_x, delta_y):
        """相对移动舵机"""
        new_x = self.current_x_angle + delta_x
        new_y = self.current_y_angle + delta_y
        return self.set_angle(new_x, new_y)
    
    def get_current_angles(self):
        """获取当前角度"""
        return (self.current_x_angle, self.current_y_angle)
    
    def center_servos(self):
        """舵机回中"""
        return self.set_angle(Config.SERVO_CENTER_ANGLE, Config.SERVO_CENTER_ANGLE)
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.servo_x:
                self.servo_x.enable(False)
                del self.servo_x
            if self.servo_y:
                self.servo_y.enable(False)
                del self.servo_y
            print("舵机资源已清理")
        except Exception as e:
            print(f"舵机清理错误: {e}")

# ==================== 摄像头管理 ====================
class CameraManager:
    def __init__(self):
        self.cam = None
        self.disp = None
        self.setup_camera()
    
    def setup_camera(self):
        """初始化摄像头"""
        try:
            self.cam = camera.Camera(Config.CAMERA_WIDTH, Config.CAMERA_HEIGHT)
            self.disp = display.Display()
            print(f"摄像头初始化: {Config.CAMERA_WIDTH}x{Config.CAMERA_HEIGHT}")
            return True
        except Exception as e:
            print(f"摄像头初始化失败: {e}")
            return False
    
    def read_frame(self):
        """读取一帧图像"""
        if self.cam:
            return self.cam.read()
        return None
    
    def show_frame(self, img):
        """显示图像"""
        if self.disp and img:
            self.disp.show(img)

# ==================== 系统状态管理 ====================
class SystemState:
    def __init__(self):
        self.rectangle_found = False
        self.tracking_rect = False
        
        # 矩形跟踪状态
        self.current_rect = None
        self.rect_history = []
        self.rect_lost_count = 0
        self.last_rect_center = None
        self.last_send_time = 0
        
        # 控制状态
        self.target_center = (Config.CAMERA_WIDTH // 2, Config.CAMERA_HEIGHT // 2)
        self.last_error = (0, 0)
        
        # 性能统计
        self.frame_count = 0
        self.fps = 0
        self.last_fps_time = time.time()
    
    def update_fps(self):
        """更新FPS统计"""
        self.frame_count += 1
        current_time = time.time()
        if current_time - self.last_fps_time >= 1.0:
            self.fps = self.frame_count
            self.frame_count = 0
            self.last_fps_time = current_time
    
    def should_send_data(self):
        """检查是否应该发送数据"""
        current_time = time.time() * 1000
        if current_time - self.last_send_time >= Config.SEND_INTERVAL:
            self.last_send_time = current_time
            return True
        return False
    
    def update_rect_tracking(self, rect):
        """更新矩形跟踪状态"""
        if rect:
            self.current_rect = rect
            self.rect_lost_count = 0
            
            # 计算矩形中心
            center = (rect.x() + rect.w() // 2, rect.y() + rect.h() // 2)
            self.last_rect_center = center
            
            # 添加到历史记录
            self.rect_history.append({
                'rect': rect,
                'center': center,
                'timestamp': time.time()
            })
            
            # 限制历史长度
            if len(self.rect_history) > 3:
                self.rect_history.pop(0)
        else:
            self.rect_lost_count += 1
            self.current_rect = None
    
    def is_rect_lost(self):
        """检查矩形是否丢失"""
        return self.rect_lost_count > Config.RECT_LOST_THRESHOLD

# ==================== 矩形处理算法 ====================
def calculate_rect_similarity(rect1, rect2):
    """计算两个矩形的相似度"""
    if not rect1 or not rect2:
        return 0.0
    
    # 计算面积相似度
    area1 = rect1.w() * rect1.h()
    area2 = rect2.w() * rect2.h()
    area_ratio = min(area1, area2) / max(area1, area2) if max(area1, area2) > 0 else 0
    
    # 计算中心距离
    center1 = (rect1.x() + rect1.w() // 2, rect1.y() + rect1.h() // 2)
    center2 = (rect2.x() + rect2.w() // 2, rect2.y() + rect2.h() // 2)
    distance = calculate_distance(center1, center2)
    
    # 距离相似度
    distance_similarity = max(0, 1 - distance / Config.MAX_RECT_DISTANCE)
    
    return (area_ratio * 0.6 + distance_similarity * 0.4)

def find_best_matching_rect(rects, last_rect):
    """找到与上一个矩形最匹配的矩形"""
    if not rects or not last_rect:
        return None
    
    best_rect = None
    best_similarity = 0
    
    for rect in rects:
        similarity = calculate_rect_similarity(rect, last_rect)
        if similarity > best_similarity and similarity > Config.RECT_SIMILARITY_THRESHOLD:
            best_similarity = similarity
            best_rect = rect
    
    return best_rect

# ==================== 主程序类 ====================
class ServoVisionTracker:
    def __init__(self):
        self.camera_manager = CameraManager()
        self.servo_controller = ServoController()
        self.system_state = SystemState()
    
    def run(self):
        """主运行循环"""
        print("MaixPy 舵机视觉跟踪系统启动...")
        print(f"分辨率: {Config.CAMERA_WIDTH}x{Config.CAMERA_HEIGHT}")
        print("按Ctrl+C退出程序")
        
        # 舵机回中
        self.servo_controller.center_servos()
        time.sleep(1)
        
        while not app.need_exit():
            try:
                img = self.camera_manager.read_frame()
                if not img:
                    continue
                
                self.system_state.update_fps()
                self._process_tracking(img)
                self.camera_manager.show_frame(img)
                
                # 内存管理
                if self.system_state.frame_count % 100 == 0:
                    if len(self.system_state.rect_history) > 2:
                        self.system_state.rect_history = self.system_state.rect_history[-2:]
                
            except MemoryError as e:
                print(f"内存错误: {e}")
                self.system_state.rect_history.clear()
                continue
            except Exception as e:
                print(f"主循环错误: {e}")
                continue
    
    def _process_tracking(self, img):
        """处理跟踪逻辑"""
        try:
            rects = img.find_rects(threshold=Config.RECT_THRESHOLD)
        except Exception as e:
            print(f"矩形检测错误: {e}")
            rects = []
        
        # 显示状态信息
        self._draw_status_info(img, len(rects))
        
        # 绘制中心十字线
        self._draw_center_crosshair(img)
        
        # 跟踪逻辑
        if Config.DYNAMIC_TRACKING and self.system_state.current_rect:
            # 寻找最匹配的矩形
            best_rect = find_best_matching_rect(rects, self.system_state.current_rect)
            if best_rect:
                self.system_state.update_rect_tracking(best_rect)
                self._track_rectangle(img, best_rect)
            else:
                self.system_state.update_rect_tracking(None)
                if self.system_state.is_rect_lost():
                    print("目标丢失，搜索新目标...")
                    self.system_state.rectangle_found = False
                    self.system_state.tracking_rect = False
        else:
            # 初始检测
            for rect in rects:
                # 绘制检测到的矩形
                img.draw_rect(rect.x(), rect.y(), rect.w(), rect.h(), 
                             color=image.Color.from_rgb(255, 0, 0), thickness=2)
                
                # 选择最大的矩形作为目标
                if not self.system_state.current_rect or (rect.w() * rect.h()) > (self.system_state.current_rect.w() * self.system_state.current_rect.h()):
                    self.system_state.rectangle_found = True
                    self.system_state.tracking_rect = True
                    self.system_state.update_rect_tracking(rect)
                    print("锁定新目标")
    
    def _track_rectangle(self, img, rect):
        """跟踪矩形并控制舵机"""
        # 绘制跟踪的矩形
        img.draw_rect(rect.x(), rect.y(), rect.w(), rect.h(), 
                     color=image.Color.from_rgb(0, 255, 0), thickness=3)
        
        # 计算矩形中心
        rect_center_x = rect.x() + rect.w() // 2
        rect_center_y = rect.y() + rect.h() // 2
        
        # 绘制矩形中心点
        img.draw_circle(rect_center_x, rect_center_y, 8, 
                       color=image.Color.from_rgb(0, 255, 0), thickness=-1)
        
        # 计算误差
        screen_center_x = Config.CAMERA_WIDTH // 2
        screen_center_y = Config.CAMERA_HEIGHT // 2
        
        error_x = rect_center_x - screen_center_x
        error_y = rect_center_y - screen_center_y
        
        # 绘制误差线
        img.draw_line(screen_center_x, screen_center_y, rect_center_x, rect_center_y,
                     color=image.Color.from_rgb(255, 255, 0), thickness=2)
        
        # 控制舵机
        self._control_servos(error_x, error_y)
        
        # 显示跟踪信息
        track_info = f"Target: {rect_center_x},{rect_center_y}"
        img.draw_string(5, 50, track_info, 
                       color=image.Color.from_rgb(0, 255, 0), scale=1.2)
        
        error_info = f"Error: {error_x},{error_y}"
        img.draw_string(5, 65, error_info, 
                       color=image.Color.from_rgb(255, 255, 0), scale=1.2)
        
        # 显示舵机角度
        angles = self.servo_controller.get_current_angles()
        servo_info = f"Servo: {angles[0]:.1f},{angles[1]:.1f}"
        img.draw_string(5, 80, servo_info, 
                       color=image.Color.from_rgb(0, 255, 255), scale=1.2)
    
    def _control_servos(self, error_x, error_y):
        """根据误差控制舵机"""
        # 检查死区
        if abs(error_x) < Config.CONTROL_DEADZONE and abs(error_y) < Config.CONTROL_DEADZONE:
            return
        
        # 计算控制量
        delta_x = -error_x * Config.CONTROL_SENSITIVITY  # X轴反向
        delta_y = error_y * Config.CONTROL_SENSITIVITY   # Y轴正向
        
        # 限制最大步长
        delta_x = clamp(delta_x, -Config.MAX_STEP, Config.MAX_STEP)
        delta_y = clamp(delta_y, -Config.MAX_STEP, Config.MAX_STEP)
        
        # 移动舵机
        if self.system_state.should_send_data():
            self.servo_controller.move_relative(delta_x, delta_y)
    
    def _draw_status_info(self, img, rect_count):
        """绘制状态信息"""
        status_text = "Servo Vision Tracker"
        img.draw_string(5, 5, status_text, 
                       color=image.Color.from_rgb(255, 255, 255), scale=1.5)
        
        img.draw_string(5, 20, f"FPS: {self.system_state.fps}", 
                       color=image.Color.from_rgb(255, 255, 255), scale=1.2)
        
        img.draw_string(5, 35, f"Rects: {rect_count}", 
                       color=image.Color.from_rgb(255, 255, 0), scale=1.2)
    
    def _draw_center_crosshair(self, img):
        """绘制中心十字线"""
        center_x = Config.CAMERA_WIDTH // 2
        center_y = Config.CAMERA_HEIGHT // 2
        
        # 绘制十字线
        img.draw_line(center_x - 20, center_y, center_x + 20, center_y,
                     color=image.Color.from_rgb(255, 0, 255), thickness=2)
        img.draw_line(center_x, center_y - 20, center_x, center_y + 20,
                     color=image.Color.from_rgb(255, 0, 255), thickness=2)
        
        # 绘制中心圆
        img.draw_circle(center_x, center_y, 5, 
                       color=image.Color.from_rgb(255, 0, 255), thickness=2)
    
    def cleanup(self):
        """清理资源"""
        print("正在清理资源...")
        self.servo_controller.center_servos()
        time.sleep(0.5)
        self.servo_controller.cleanup()

# ==================== 主程序入口 ====================
def main():
    """主函数"""
    tracker = None
    try:
        tracker = ServoVisionTracker()
        tracker.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
    finally:
        if tracker:
            tracker.cleanup()
        print("舵机视觉跟踪程序结束")

if __name__ == "__main__":
    main()