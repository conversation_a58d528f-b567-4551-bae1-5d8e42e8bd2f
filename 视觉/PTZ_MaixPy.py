#!/usr/bin/env python3
"""
MaixPy PTZ 视觉跟踪系统
基于现代MaixPy API的完整实现
"""

import time
import math
from maix import camera, display, image, app, uart

# ==================== 配置参数 ====================
class Config:
    # 摄像头参数 - 降低分辨率解决内存问题
    CAMERA_WIDTH = 320
    CAMERA_HEIGHT = 240
    
    # 矩形检测参数 - 针对低分辨率优化
    RECT_THRESHOLD = 5000   # 降低阈值适应小分辨率
    SHRINK_VALUE = 4.0      # 减小收缩值
    MAX_LOOPS = 3
    MAX_DIFFERENCE = 8      # 减小差异容忍度
    MIN_BORDER_WIDTH = 3
    
    # 动态跟踪参数 - 针对低分辨率优化
    DYNAMIC_TRACKING = True
    RECT_LOST_THRESHOLD = 5   # 减少丢失阈值，更快响应
    RECT_SIMILARITY_THRESHOLD = 0.6  # 降低相似度要求
    MAX_RECT_DISTANCE = 30    # 减小最大移动距离
    
    # 数据发送参数
    SEND_INTERVAL = 100
    
    # 串口参数
    UART_PORT = "/dev/ttyS1"
    UART_BAUDRATE = 115200
    FRAME_HEADER1 = 0xA5
    FRAME_HEADER2 = 0xA6

# ==================== 数学工具函数 ====================
def calculate_distance(point1, point2):
    """计算两点间的欧几里得距离"""
    if not point1 or not point2:
        return float('inf')
    dx = point1[0] - point2[0]
    dy = point1[1] - point2[1]
    return math.sqrt(dx * dx + dy * dy)

def calculate_midpoint(p1, p2):
    """计算两点中点"""
    return ((p1[0] + p2[0]) / 2, (p1[1] + p2[1]) / 2)

# ==================== 摄像头管理 ====================
class CameraManager:
    def __init__(self):
        self.cam = None
        self.disp = None
        self.setup_camera()
    
    def setup_camera(self):
        """初始化摄像头"""
        try:
            self.cam = camera.Camera(Config.CAMERA_WIDTH, Config.CAMERA_HEIGHT)
            self.disp = display.Display()
            print(f"摄像头初始化: {Config.CAMERA_WIDTH}x{Config.CAMERA_HEIGHT}")
            return True
        except Exception as e:
            print("摄像头初始化失败:", e)
            return False
    
    def read_frame(self):
        """读取一帧图像"""
        if self.cam:
            return self.cam.read()
        return None
    
    def show_frame(self, img):
        """显示图像"""
        if self.disp and img:
            self.disp.show(img)

# ==================== 串口通信管理 ====================
class UARTManager:
    def __init__(self):
        self.uart_dev = None
        self.init_uart()
    
    def init_uart(self):
        """初始化串口"""
        try:
            self.uart_dev = uart.UART(Config.UART_PORT, Config.UART_BAUDRATE)
            print(f"串口初始化成功: {Config.UART_PORT}")
            return True
        except Exception as e:
            print("串口初始化失败:", e)
            return False
    
    def send_data(self, data):
        """发送单字节数据"""
        if not self.uart_dev:
            return False
        try:
            packet = bytes([Config.FRAME_HEADER1, Config.FRAME_HEADER2, data])
            self.uart_dev.write(packet)
            return True
        except Exception as e:
            print("数据发送失败:", e)
            return False
    
    def send_rect_data(self, rect_center, rect_size):
        """发送矩形数据"""
        if not rect_center or not rect_size or not self.uart_dev:
            return False
        
        try:
            coords = [
                int(rect_center[0]) & 0xFF,  # 中心X
                int(rect_center[1]) & 0xFF,  # 中心Y
                int(rect_size[0]) & 0xFF,    # 宽度
                int(rect_size[1]) & 0xFF     # 高度
            ]
            
            for coord in coords:
                if not self.send_data(coord):
                    return False
            return True
        except Exception as e:
            print("矩形数据发送失败:", e)
            return False

# ==================== 系统状态管理 ====================
class SystemState:
    def __init__(self):
        self.rectangle_found = False
        
        # 矩形检测状态
        self.stable_original_corners = None
        self.stable_shrunk_corners = None
        self.stable_count = 0
        
        # 动态矩形跟踪状态
        self.current_rect = None
        self.rect_history = []
        self.rect_lost_count = 0
        self.last_rect_center = None
        self.tracking_rect = False
        self.last_send_time = 0
        
        # 性能统计
        self.frame_count = 0
        self.fps = 0
        self.last_fps_time = time.time()
    

    
    def update_fps(self):
        """更新FPS统计"""
        self.frame_count += 1
        current_time = time.time()
        if current_time - self.last_fps_time >= 1.0:
            self.fps = self.frame_count
            self.frame_count = 0
            self.last_fps_time = current_time
    
    def should_send_data(self):
        """检查是否应该发送数据"""
        current_time = time.time() * 1000  # 转换为毫秒
        if current_time - self.last_send_time >= Config.SEND_INTERVAL:
            self.last_send_time = current_time
            return True
        return False
    
    def update_rect_tracking(self, rect):
        """更新矩形跟踪状态"""
        if rect:
            self.current_rect = rect
            self.rect_lost_count = 0
            
            # 计算矩形中心
            center = (rect.x() + rect.w() // 2, rect.y() + rect.h() // 2)
            self.last_rect_center = center
            
            # 添加到历史记录
            self.rect_history.append({
                'rect': rect,
                'center': center,
                'timestamp': time.time()
            })
            
            # 限制历史长度 - 减少内存使用
            if len(self.rect_history) > 5:
                self.rect_history.pop(0)
        else:
            self.rect_lost_count += 1
            self.current_rect = None
    
    def is_rect_lost(self):
        """检查矩形是否丢失"""
        return self.rect_lost_count > Config.RECT_LOST_THRESHOLD

# ==================== 矩形处理算法 ====================
def shrink_rectangle_corners(corners, shrink_value):
    """精确收缩矩形角点"""
    try:
        shrunk_corners = []
        
        # 计算矩形中心点
        center_x = sum(corner[0] for corner in corners) / 4
        center_y = sum(corner[1] for corner in corners) / 4
        
        for corner in corners:
            x, y = corner
            
            # 计算从角点到中心的向量
            dx = center_x - x
            dy = center_y - y
            
            distance = math.sqrt(dx * dx + dy * dy)
            
            if distance > 0:
                unit_x = dx / distance
                unit_y = dy / distance
                
                new_x = x + unit_x * shrink_value
                new_y = y + unit_y * shrink_value
                
                shrunk_corners.append((new_x, new_y))
            else:
                shrunk_corners.append(corner)
        
        return shrunk_corners
    
    except Exception as e:
        print("矩形收缩错误:", e)
        return corners

def process_rectangle(rect):
    """处理矩形，返回原始和收缩后的角点"""
    try:
        original_corners = rect.corners()
        shrunk_corners = shrink_rectangle_corners(original_corners, Config.SHRINK_VALUE)
        shrunk_corners = [(int(x), int(y)) for x, y in shrunk_corners]
        
        return original_corners, shrunk_corners
    
    except Exception as e:
        print("矩形处理错误:", e)
        return None, None

def calculate_rect_similarity(rect1, rect2):
    """计算两个矩形的相似度"""
    if not rect1 or not rect2:
        return 0.0
    
    # 计算面积相似度
    area1 = rect1.w() * rect1.h()
    area2 = rect2.w() * rect2.h()
    area_ratio = min(area1, area2) / max(area1, area2) if max(area1, area2) > 0 else 0
    
    # 计算中心距离
    center1 = (rect1.x() + rect1.w() // 2, rect1.y() + rect1.h() // 2)
    center2 = (rect2.x() + rect2.w() // 2, rect2.y() + rect2.h() // 2)
    distance = calculate_distance(center1, center2)
    
    # 距离相似度（距离越小相似度越高）
    distance_similarity = max(0, 1 - distance / Config.MAX_RECT_DISTANCE)
    
    # 综合相似度
    return (area_ratio * 0.6 + distance_similarity * 0.4)

def find_best_matching_rect(rects, last_rect):
    """找到与上一个矩形最匹配的矩形"""
    if not rects or not last_rect:
        return None
    
    best_rect = None
    best_similarity = 0
    
    for rect in rects:
        similarity = calculate_rect_similarity(rect, last_rect)
        if similarity > best_similarity and similarity > Config.RECT_SIMILARITY_THRESHOLD:
            best_similarity = similarity
            best_rect = rect
    
    return best_rect

def check_corners_stability(new_original, new_shrunk, state):
    """检查角点稳定性（支持动态跟踪）"""
    if not Config.DYNAMIC_TRACKING:
        # 原始静态检测逻辑
        if state.stable_original_corners is None:
            state.stable_original_corners = new_original
            state.stable_shrunk_corners = new_shrunk
            state.stable_count = 1
            return False
        
        stable = True
        for i in range(4):
            for j in range(2):
                if (abs(new_original[i][j] - state.stable_original_corners[i][j]) > Config.MAX_DIFFERENCE or
                    abs(new_shrunk[i][j] - state.stable_shrunk_corners[i][j]) > Config.MAX_DIFFERENCE):
                    stable = False
                    break
            if not stable:
                break
        
        if stable:
            state.stable_count += 1
        else:
            state.stable_original_corners = new_original
            state.stable_shrunk_corners = new_shrunk
            state.stable_count = 1
        
        return state.stable_count >= Config.MAX_LOOPS
    else:
        # 动态跟踪逻辑 - 更快确认
        state.stable_original_corners = new_original
        state.stable_shrunk_corners = new_shrunk
        state.stable_count += 1
        return state.stable_count >= Config.MAX_LOOPS



# ==================== 主程序类 ====================
class PTZTracker:
    def __init__(self):
        self.camera_manager = CameraManager()
        self.uart_manager = UARTManager()
        self.system_state = SystemState()
    
    def run(self):
        """主运行循环"""
        print("MaixPy 动态矩形跟踪系统启动...")
        print(f"分辨率: {Config.CAMERA_WIDTH}x{Config.CAMERA_HEIGHT}")
        
        while not app.need_exit():
            try:
                img = self.camera_manager.read_frame()
                if not img:
                    continue
                
                self.system_state.update_fps()
                self._process_rectangle_tracking(img)
                self.camera_manager.show_frame(img)
                
                # 内存管理 - 每100帧清理一次历史
                if self.system_state.frame_count % 100 == 0:
                    if len(self.system_state.rect_history) > 3:
                        self.system_state.rect_history = self.system_state.rect_history[-3:]
                
            except MemoryError as e:
                print(f"内存错误: {e}")
                # 清理历史数据
                self.system_state.rect_history.clear()
                continue
            except Exception as e:
                print(f"主循环错误: {e}")
                continue
    
    def _process_rectangle_tracking(self, img):
        """处理矩形跟踪（主要功能）"""
        try:
            rects = img.find_rects(threshold=Config.RECT_THRESHOLD)
        except Exception as e:
            print(f"矩形检测错误: {e}")
            rects = []
        
        # 显示跟踪状态 - 使用较小的字体节省内存
        status_text = "Rect Tracking"
        img.draw_string(5, 5, status_text, 
                       color=image.Color.from_rgb(255, 255, 255), scale=1.5)
        img.draw_string(5, 20, f"FPS: {self.system_state.fps}", 
                       color=image.Color.from_rgb(255, 255, 255), scale=1.5)
        
        # 显示检测到的矩形数量
        img.draw_string(5, 35, f"Rects: {len(rects)}", 
                       color=image.Color.from_rgb(255, 255, 0), scale=1.2)

        # 动态跟踪逻辑
        if Config.DYNAMIC_TRACKING and self.system_state.current_rect:
            # 寻找最匹配的矩形
            best_rect = find_best_matching_rect(rects, self.system_state.current_rect)
            if best_rect:
                self.system_state.update_rect_tracking(best_rect)
                self._draw_tracked_rectangle(img, best_rect)
                self._send_rect_data(best_rect)
            else:
                self.system_state.update_rect_tracking(None)
                if self.system_state.is_rect_lost():
                    print("矩形跟踪丢失，重新搜索...")
                    self.system_state.rectangle_found = False
                    self.system_state.tracking_rect = False
        else:
            # 初始检测
            for rect in rects:
                # 绘制所有检测到的矩形
                img.draw_rect(rect.x(), rect.y(), rect.w(), rect.h(), 
                             color=image.Color.from_rgb(255, 0, 0), thickness=2)
                
                original_corners, shrunk_corners = process_rectangle(rect)
                if not original_corners or not shrunk_corners:
                    continue

                if check_corners_stability(original_corners, shrunk_corners, self.system_state):
                    self.system_state.rectangle_found = True
                    self.system_state.tracking_rect = True
                    self.system_state.update_rect_tracking(rect)
                    print("矩形锁定成功，开始跟踪")
                    break
        
        # 绘制矩形框架
        self._draw_rectangle_frames(img)
    
    def _draw_tracked_rectangle(self, img, rect):
        """绘制跟踪到的矩形"""
        # 绘制当前跟踪的矩形（绿色边框表示正在跟踪）
        img.draw_rect(rect.x(), rect.y(), rect.w(), rect.h(), 
                     color=image.Color.from_rgb(0, 255, 0), thickness=3)
        
        # 显示矩形中心点
        center_x = rect.x() + rect.w() // 2
        center_y = rect.y() + rect.h() // 2
        img.draw_circle(center_x, center_y, 8, 
                       color=image.Color.from_rgb(0, 255, 0), thickness=-1)
        
        # 显示跟踪信息 - 使用较小字体
        track_info = f"Track: {center_x},{center_y}"
        img.draw_string(5, 50, track_info, 
                       color=image.Color.from_rgb(0, 255, 0), scale=1.2)
        
        # 显示矩形尺寸
        size_info = f"Size: {rect.w()}x{rect.h()}"
        img.draw_string(5, 65, size_info, 
                       color=image.Color.from_rgb(0, 255, 255), scale=1.2)
        
        # 实时更新矩形角点
        original_corners, shrunk_corners = process_rectangle(rect)
        if original_corners and shrunk_corners:
            self.system_state.stable_original_corners = original_corners
            self.system_state.stable_shrunk_corners = shrunk_corners
    
    def _send_rect_data(self, rect):
        """发送矩形数据"""
        if rect and self.system_state.should_send_data():
            center = (rect.x() + rect.w() // 2, rect.y() + rect.h() // 2)
            size = (rect.w(), rect.h())
            self.uart_manager.send_rect_data(center, size)
    

    
    def _draw_rectangle_frames(self, img):
        """绘制矩形框（支持动态更新指示）"""
        if self.system_state.stable_original_corners and self.system_state.stable_shrunk_corners:
            # 动态跟踪颜色
            outer_color = image.Color.from_rgb(0, 255, 255)  # 青色外框
            inner_color = image.Color.from_rgb(255, 100, 0)  # 橙色内框
            
            # 外框
            for i in range(4):
                p1 = self.system_state.stable_original_corners[i]
                p2 = self.system_state.stable_original_corners[(i + 1) % 4]
                img.draw_line(int(p1[0]), int(p1[1]), int(p2[0]), int(p2[1]), 
                             color=outer_color, thickness=2)

            # 内框
            for i in range(4):
                p1 = self.system_state.stable_shrunk_corners[i]
                p2 = self.system_state.stable_shrunk_corners[(i + 1) % 4]
                img.draw_line(int(p1[0]), int(p1[1]), int(p2[0]), int(p2[1]), 
                             color=inner_color, thickness=3)

            # 内框角点
            for corner in self.system_state.stable_shrunk_corners:
                img.draw_circle(int(corner[0]), int(corner[1]), 4, 
                               color=image.Color.from_rgb(255, 255, 255), thickness=-1)
            
            # 显示矩形中心轨迹 - 只显示最近几个点
            if len(self.system_state.rect_history) > 1:
                # 只绘制最近3个点的轨迹
                recent_history = self.system_state.rect_history[-3:]
                for i in range(1, len(recent_history)):
                    prev_center = recent_history[i-1]['center']
                    curr_center = recent_history[i]['center']
                    
                    img.draw_line(prev_center[0], prev_center[1], curr_center[0], curr_center[1],
                                 color=image.Color.from_rgb(255, 255, 0), thickness=1)

# ==================== 主程序入口 ====================
def main():
    """主函数"""
    try:
        tracker = PTZTracker()
        tracker.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
    finally:
        print("PTZ跟踪程序结束")

if __name__ == "__main__":
    main()