#!/usr/bin/env python3
"""
MaixPy 舵机驱动接口
支持触摸屏角度调节的舵机控制界面
"""

import time
import math
from maix import camera, display, image, app, pwm, pinmap, touchscreen

# ==================== 配置参数 ====================
class Config:
    # 显示参数
    DISPLAY_WIDTH = 522
    DISPLAY_HEIGHT = 368
    
    # 舵机控制参数
    SERVO_X_PWM_ID = 6      # X轴舵机PWM ID
    SERVO_Y_PWM_ID = 7      # Y轴舵机PWM ID
    SERVO_X_PIN = "A18"     # X轴舵机引脚 (对应PWM6)
    SERVO_Y_PIN = "A19"     # Y轴舵机引脚 (对应PWM7)
    SERVO_FREQ = 50         # 舵机PWM频率 50Hz
    
    # 舵机占空比参数 (百分比)
    SERVO_MIN_DUTY = 2.5    # 最小占空比 2.5% -> 0.5ms
    SERVO_MAX_DUTY = 12.5   # 最大占空比 12.5% -> 2.5ms
    SERVO_CENTER_DUTY = 7.5 # 中心占空比 7.5% -> 1.5ms
    
    # 舵机角度范围
    SERVO_MIN_ANGLE = 0     # 最小角度
    SERVO_MAX_ANGLE = 180   # 最大角度
    SERVO_CENTER_ANGLE = 90 # 中心角度
    
    # UI参数
    SLIDER_WIDTH = 300      # 滑块宽度
    SLIDER_HEIGHT = 40      # 滑块高度
    SLIDER_MARGIN = 50      # 滑块边距
    
    # 颜色定义
    COLOR_BG = (30, 30, 30)           # 背景色
    COLOR_SLIDER_BG = (80, 80, 80)    # 滑块背景色
    COLOR_SLIDER_FILL = (0, 150, 255) # 滑块填充色
    COLOR_TEXT = (255, 255, 255)      # 文字颜色
    COLOR_BUTTON = (100, 200, 100)    # 按钮颜色

# ==================== 数学工具函数 ====================
def clamp(value, min_val, max_val):
    """限制数值范围"""
    return max(min_val, min(max_val, value))

def map_range(value, in_min, in_max, out_min, out_max):
    """映射数值范围"""
    return (value - in_min) * (out_max - out_min) / (in_max - in_min) + out_min

# ==================== 舵机驱动类 ====================
class ServoDriver:
    def __init__(self):
        self.servo_x = None
        self.servo_y = None
        self.current_x_angle = Config.SERVO_CENTER_ANGLE
        self.current_y_angle = Config.SERVO_CENTER_ANGLE
        self.is_initialized = False
    
    def init(self):
        """初始化舵机驱动"""
        try:
            # 配置引脚功能
            pinmap.set_pin_function(Config.SERVO_X_PIN, f"PWM{Config.SERVO_X_PWM_ID}")
            pinmap.set_pin_function(Config.SERVO_Y_PIN, f"PWM{Config.SERVO_Y_PWM_ID}")
            
            # 初始化PWM
            self.servo_x = pwm.PWM(Config.SERVO_X_PWM_ID, freq=Config.SERVO_FREQ, 
                                  duty=Config.SERVO_CENTER_DUTY, enable=True)
            self.servo_y = pwm.PWM(Config.SERVO_Y_PWM_ID, freq=Config.SERVO_FREQ, 
                                  duty=Config.SERVO_CENTER_DUTY, enable=True)
            
            self.is_initialized = True
            print(f"舵机驱动初始化成功:")
            print(f"  X轴: {Config.SERVO_X_PIN} -> PWM{Config.SERVO_X_PWM_ID}")
            print(f"  Y轴: {Config.SERVO_Y_PIN} -> PWM{Config.SERVO_Y_PWM_ID}")
            print(f"  频率: {Config.SERVO_FREQ}Hz")
            
            return True
        except Exception as e:
            print(f"舵机驱动初始化失败: {e}")
            self.is_initialized = False
            return False
    
    def angle_to_duty(self, angle):
        """将角度转换为占空比"""
        angle = clamp(angle, Config.SERVO_MIN_ANGLE, Config.SERVO_MAX_ANGLE)
        duty = map_range(angle, 
                        Config.SERVO_MIN_ANGLE, Config.SERVO_MAX_ANGLE,
                        Config.SERVO_MIN_DUTY, Config.SERVO_MAX_DUTY)
        return duty
    
    def set_x_angle(self, angle):
        """设置X轴舵机角度"""
        if not self.is_initialized or not self.servo_x:
            return False
        
        try:
            angle = clamp(angle, Config.SERVO_MIN_ANGLE, Config.SERVO_MAX_ANGLE)
            duty = self.angle_to_duty(angle)
            self.servo_x.duty(duty)
            self.current_x_angle = angle
            return True
        except Exception as e:
            print(f"X轴舵机控制错误: {e}")
            return False
    
    def set_y_angle(self, angle):
        """设置Y轴舵机角度"""
        if not self.is_initialized or not self.servo_y:
            return False
        
        try:
            angle = clamp(angle, Config.SERVO_MIN_ANGLE, Config.SERVO_MAX_ANGLE)
            duty = self.angle_to_duty(angle)
            self.servo_y.duty(duty)
            self.current_y_angle = angle
            return True
        except Exception as e:
            print(f"Y轴舵机控制错误: {e}")
            return False
    
    def set_angles(self, x_angle, y_angle):
        """同时设置两轴角度"""
        x_success = self.set_x_angle(x_angle)
        y_success = self.set_y_angle(y_angle)
        return x_success and y_success
    
    def get_x_angle(self):
        """获取X轴当前角度"""
        return self.current_x_angle
    
    def get_y_angle(self):
        """获取Y轴当前角度"""
        return self.current_y_angle
    
    def get_angles(self):
        """获取两轴当前角度"""
        return (self.current_x_angle, self.current_y_angle)
    
    def center(self):
        """舵机回中"""
        return self.set_angles(Config.SERVO_CENTER_ANGLE, Config.SERVO_CENTER_ANGLE)
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.servo_x:
                self.servo_x.enable(False)
                del self.servo_x
            if self.servo_y:
                self.servo_y.enable(False)
                del self.servo_y
            self.is_initialized = False
            print("舵机驱动资源已清理")
        except Exception as e:
            print(f"舵机驱动清理错误: {e}")

# ==================== UI控制类 ====================
class ServoControlUI:
    def __init__(self):
        self.disp = display.Display()
        self.ts = touchscreen.TouchScreen()
        self.servo_driver = ServoDriver()
        
        # UI状态
        self.dragging_x = False
        self.dragging_y = False
        self.last_touch_time = 0
        
        # 计算UI布局
        self.setup_ui_layout()
    
    def setup_ui_layout(self):
        """设置UI布局"""
        # X轴滑块位置
        self.x_slider_x = (self.disp.width() - Config.SLIDER_WIDTH) // 2
        self.x_slider_y = 80
        
        # Y轴滑块位置
        self.y_slider_x = (self.disp.width() - Config.SLIDER_WIDTH) // 2
        self.y_slider_y = 160
        
        # 按钮位置
        self.center_btn_x = (self.disp.width() - 120) // 2
        self.center_btn_y = 240
        self.center_btn_w = 120
        self.center_btn_h = 40
        
        # 退出按钮位置
        self.exit_btn_x = 10
        self.exit_btn_y = 10
        self.exit_btn_w = 80
        self.exit_btn_h = 30
    
    def init(self):
        """初始化UI和舵机"""
        return self.servo_driver.init()
    
    def draw_slider(self, img, x, y, width, height, value, min_val, max_val, label):
        """绘制滑块"""
        # 绘制滑块背景
        img.draw_rect(x, y, width, height, 
                     image.Color.from_rgb(*Config.COLOR_SLIDER_BG), thickness=-1)
        
        # 计算填充宽度
        fill_width = int((value - min_val) / (max_val - min_val) * width)
        
        # 绘制填充部分
        if fill_width > 0:
            img.draw_rect(x, y, fill_width, height, 
                         image.Color.from_rgb(*Config.COLOR_SLIDER_FILL), thickness=-1)
        
        # 绘制边框
        img.draw_rect(x, y, width, height, 
                     image.Color.from_rgb(*Config.COLOR_TEXT), thickness=2)
        
        # 绘制滑块手柄
        handle_x = x + fill_width - 5
        handle_x = clamp(handle_x, x, x + width - 10)
        img.draw_rect(handle_x, y - 5, 10, height + 10, 
                     image.Color.from_rgb(*Config.COLOR_TEXT), thickness=-1)
        
        # 绘制标签和数值
        label_text = f"{label}: {value:.1f}°"
        img.draw_string(x, y - 25, label_text, 
                       image.Color.from_rgb(*Config.COLOR_TEXT), scale=1.5)
    
    def draw_button(self, img, x, y, width, height, text, color):
        """绘制按钮"""
        # 绘制按钮背景
        img.draw_rect(x, y, width, height, 
                     image.Color.from_rgb(*color), thickness=-1)
        
        # 绘制按钮边框
        img.draw_rect(x, y, width, height, 
                     image.Color.from_rgb(*Config.COLOR_TEXT), thickness=2)
        
        # 绘制按钮文字
        text_size = image.string_size(text, scale=1.5)
        text_x = x + (width - text_size.width()) // 2
        text_y = y + (height - text_size.height()) // 2
        img.draw_string(text_x, text_y, text, 
                       image.Color.from_rgb(*Config.COLOR_TEXT), scale=1.5)
    
    def draw_ui(self, img):
        """绘制完整UI"""
        # 清空背景
        img.draw_rect(0, 0, img.width(), img.height(), 
                     image.Color.from_rgb(*Config.COLOR_BG), thickness=-1)
        
        # 绘制标题
        title = "Servo Control Interface"
        title_size = image.string_size(title, scale=2)
        title_x = (img.width() - title_size.width()) // 2
        img.draw_string(title_x, 20, title, 
                       image.Color.from_rgb(*Config.COLOR_TEXT), scale=2)
        
        # 绘制X轴滑块
        self.draw_slider(img, self.x_slider_x, self.x_slider_y, 
                        Config.SLIDER_WIDTH, Config.SLIDER_HEIGHT,
                        self.servo_driver.get_x_angle(), 
                        Config.SERVO_MIN_ANGLE, Config.SERVO_MAX_ANGLE, "X-Axis")
        
        # 绘制Y轴滑块
        self.draw_slider(img, self.y_slider_x, self.y_slider_y, 
                        Config.SLIDER_WIDTH, Config.SLIDER_HEIGHT,
                        self.servo_driver.get_y_angle(), 
                        Config.SERVO_MIN_ANGLE, Config.SERVO_MAX_ANGLE, "Y-Axis")
        
        # 绘制居中按钮
        self.draw_button(img, self.center_btn_x, self.center_btn_y, 
                        self.center_btn_w, self.center_btn_h, 
                        "Center", Config.COLOR_BUTTON)
        
        # 绘制退出按钮
        self.draw_button(img, self.exit_btn_x, self.exit_btn_y, 
                        self.exit_btn_w, self.exit_btn_h, 
                        "Exit", (200, 100, 100))
        
        # 显示状态信息
        status = "Ready" if self.servo_driver.is_initialized else "Error"
        status_color = Config.COLOR_TEXT if self.servo_driver.is_initialized else (255, 100, 100)
        img.draw_string(10, img.height() - 30, f"Status: {status}", 
                       image.Color.from_rgb(*status_color), scale=1.2)
    
    def handle_touch(self, x, y, pressed):
        """处理触摸事件"""
        current_time = time.time()
        
        if pressed:
            # 检查退出按钮
            if (self.exit_btn_x <= x <= self.exit_btn_x + self.exit_btn_w and
                self.exit_btn_y <= y <= self.exit_btn_y + self.exit_btn_h):
                app.set_exit_flag(True)
                return
            
            # 检查居中按钮
            if (self.center_btn_x <= x <= self.center_btn_x + self.center_btn_w and
                self.center_btn_y <= y <= self.center_btn_y + self.center_btn_h):
                self.servo_driver.center()
                return
            
            # 检查X轴滑块
            if (self.x_slider_x <= x <= self.x_slider_x + Config.SLIDER_WIDTH and
                self.x_slider_y <= y <= self.x_slider_y + Config.SLIDER_HEIGHT):
                self.dragging_x = True
                # 计算角度
                relative_x = x - self.x_slider_x
                angle = map_range(relative_x, 0, Config.SLIDER_WIDTH, 
                                Config.SERVO_MIN_ANGLE, Config.SERVO_MAX_ANGLE)
                self.servo_driver.set_x_angle(angle)
                return
            
            # 检查Y轴滑块
            if (self.y_slider_x <= x <= self.y_slider_x + Config.SLIDER_WIDTH and
                self.y_slider_y <= y <= self.y_slider_y + Config.SLIDER_HEIGHT):
                self.dragging_y = True
                # 计算角度
                relative_x = x - self.y_slider_x
                angle = map_range(relative_x, 0, Config.SLIDER_WIDTH, 
                                Config.SERVO_MIN_ANGLE, Config.SERVO_MAX_ANGLE)
                self.servo_driver.set_y_angle(angle)
                return
        else:
            # 释放拖拽状态
            self.dragging_x = False
            self.dragging_y = False
        
        # 处理拖拽
        if self.dragging_x:
            relative_x = clamp(x - self.x_slider_x, 0, Config.SLIDER_WIDTH)
            angle = map_range(relative_x, 0, Config.SLIDER_WIDTH, 
                            Config.SERVO_MIN_ANGLE, Config.SERVO_MAX_ANGLE)
            self.servo_driver.set_x_angle(angle)
        
        if self.dragging_y:
            relative_x = clamp(x - self.y_slider_x, 0, Config.SLIDER_WIDTH)
            angle = map_range(relative_x, 0, Config.SLIDER_WIDTH, 
                            Config.SERVO_MIN_ANGLE, Config.SERVO_MAX_ANGLE)
            self.servo_driver.set_y_angle(angle)
    
    def run(self):
        """运行UI主循环"""
        print("舵机控制界面启动...")
        
        if not self.init():
            print("初始化失败")
            return
        
        # 舵机回中
        self.servo_driver.center()
        
        while not app.need_exit():
            try:
                # 创建图像
                img = image.Image(self.disp.width(), self.disp.height())
                
                # 绘制UI
                self.draw_ui(img)
                
                # 处理触摸
                x, y, pressed = self.ts.read()
                self.handle_touch(x, y, pressed)
                
                # 显示图像
                self.disp.show(img)
                
                # 短暂延时
                time.sleep_ms(50)
                
            except Exception as e:
                print(f"UI循环错误: {e}")
                continue
    
    def cleanup(self):
        """清理资源"""
        self.servo_driver.cleanup()

# ==================== 主程序入口 ====================
def main():
    """主函数"""
    ui = None
    try:
        ui = ServoControlUI()
        ui.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
    finally:
        if ui:
            ui.cleanup()
        print("舵机控制界面程序结束")

if __name__ == "__main__":
    main()